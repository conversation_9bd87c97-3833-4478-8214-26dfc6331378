using Api.Filter;
using AppService.Dtos.member;
using BPM.Extras.Youzan.Request;
using BPM.Extras.Youzan.Responses;
using BPM.Extras.Youzan.Services;
using BPM.Helpers;
using BPM.Logs.Contents;
using Domain.Shared;
using Essensoft.Paylink.Alipay.Domain;
using System.Collections.Generic;
using System.Linq;
using Web.Api.Common;
using Web.Api.Configs;
using System.Data;
using Newtonsoft.Json;
/// <summary>
///  会员控制器
/// </summary>
[Route("api/member")]
[Authorize]
public class memberController : ApiControllerBase
{
    /// <summary>
    /// 会员应用服务
    /// </summary>
    private readonly IMemberAppService _memberAppService;

    /// <summary>
    /// 有赞服务接口
    /// </summary>
    private readonly IYouzanService _youzanService;

    /// <summary>
    /// 缓存服务
    /// </summary>
    private readonly ICache _cache;

    /// <summary>
    /// 初始化服务
    /// </summary>
    /// <param name="prodcutAppService">应用服务</param>
    public memberController(IMemberAppService memberAppService, IServiceConfigProvider serviceConfigProvider
        , ICache cache, IConfiguration configuration, IYouzanService youzanService)
    {
        _youzanService = youzanService;
        _memberAppService = memberAppService;
        _cache = cache;
    }

    /// <summary>
    /// 获取会员列表[内部版]
    /// </summary>
    /// <param name="request">请求参数</param>
    /// <returns></returns>
    [HttpPost, Route("get_member_list")]
    public async Task<IActionResult> getPageMemberList([FromBody] baseRequest request)
    {
        var query = new memberQuery();
        query.Page = request.page_index;
        query.PageSize = request.page_size;
        query.Order = "CODE DESC";
        var result = await _memberAppService.getPageMemberListNew(query);
        var jsonData = new
        {
            data = result.Data, //数据
            total_page = result.PageCount,//总页数
            page = result.Page,//当前页
            records = result.TotalCount,//总记录数
        };
        return Success(jsonData);
    }

    /// <summary>
    /// 获取会员等级列表[内部版]
    /// </summary>
    /// <param name="request">请求参数</param>
    /// <returns></returns>
    [HttpPost, Route("get_member_grade_list")]
    public async Task<IActionResult> getPageMemberGradeList([FromBody] baseRequest request)
    {
        var query = new memberQuery();
        query.Page = request.page_index;
        query.PageSize = request.page_size;
        query.Order = "GRADE DESC";
        var result = await _memberAppService.getPageMemberGradeList(query);
        var jsonData = new
        {
            data = result.Data, //数据
            total_page = result.PageCount,//总页数
            page = result.Page,//当前页
            records = result.TotalCount,//总记录数
        };
        return Success(jsonData);
    }

    /// <summary>
    /// 获取会员信息[内部版]
    /// </summary>
    /// <param name="request">请求参数</param>
    /// <returns></returns>
    [HttpPost, Route("get_member_info")]
    public async Task<IActionResult> getMemberInfo([FromBody] memberRequest request)
    {
        var data = await _memberAppService.getMemberInfo(request);
        var jsonData = new
        {
            data = data, //数据
            total_page = 1,//总页数
            page = 1,//当前页
            records = data.Count,//总记录数
        };
        return Success(jsonData);
    }

    ///// <summary>
    ///// 获取会员信息【有赞版】
    ///// </summary>
    ///// <param name="request">请求参数</param>
    ///// <returns></returns>
    //[HttpPost, Route("v1/get_member_info")]
    //public async Task<IActionResult> getMemberInfoV1([FromBody] memberRequest request)
    //{
    //    var memberData = new List<memberDto>();
    //    //默认手机号查询
    //    var memberInfo = await getYzMemberInfo(request.code);
    //    if (memberInfo.Count == 0)
    //    {
    //        // 按卡号查询 换取手机号 
    //        var costomer_equity_info = await _memberAppService.getCustomerEquity(request.code);
    //        if (costomer_equity_info != null)
    //        {
    //            memberInfo = await getYzMemberInfo(costomer_equity_info.phone);
    //            if (memberInfo.Count == 0)
    //            {
    //                // 查询本地
    //                memberData = await _memberAppService.getMemberInfo(request);
    //            }
    //            else
    //            {
    //                memberData = memberInfo;
    //            }
    //        }
    //        else
    //            // 查询本地
    //            memberData = await _memberAppService.getMemberInfo(request);
    //    }
    //    else
    //        memberData = memberInfo;
    //    var jsonData = new
    //    {
    //        data = memberData, //数据
    //        total_page = 1,//总页数
    //        page = 1,//当前页
    //        records = memberData.Count,//总记录数
    //    };
    //    return Success(jsonData);
    //}




    /// <summary>
    /// 获取会员信息【有赞版】
    /// </summary>
    /// <param name="request">请求参数</param>
    /// <returns></returns>
    [HttpPost, Route("v1/get_member_info")]
    public async Task<IActionResult> getMemberInfoV1([FromBody] memberRequest request)
    {
        var memberData = new List<memberDto>();
        var phoneToQuery = string.Empty;

        // 判断是否为18位code
        if (request.code.Length == 18)
        {
            // 调用18位code专用接口获取手机号
            var customerInfo = await getYzCustomerInfoBy18DigitCode(request.code);
            if (customerInfo.success && !string.IsNullOrEmpty(customerInfo.mobile))
            {
                phoneToQuery = customerInfo.mobile;
                WriteLog("会员查询", "通过用户权益卡号获取会员信息,获取手机号", request.ToJson(), customerInfo.mobile);
            }
        }

        // 查询有赞会员信息
        var yzMemberResult = await getYzMemberInfo(string.IsNullOrEmpty(phoneToQuery) ? request.code : phoneToQuery);
        WriteLog("会员查询", "有赞权益卡查询结果", request.ToJson(), yzMemberResult.ValidCards.ToJson());

        // 判断是否获取到权益卡（包含各种状态）
        if (yzMemberResult.isAuthMobile && yzMemberResult.ValidCards.Count > 0)
        {
            // 检查是否有有效权益卡（card_state == 1 且未过期）
            var validCards = yzMemberResult.ValidCards.Where(card =>
                card.STATUS == 1 && // card_state == 1 使用中
                card.EXPDATE.HasValue && card.EXPDATE.Value > DateTime.Now // 未过期
            ).ToList();

            if (validCards.Count > 0)
            {
                // 有有效权益卡，返回所有有赞权益卡数据
                memberData = yzMemberResult.ValidCards;
                // 设置有赞数据来源
                foreach (var member in memberData)
                {
                    member.SOURCE = "有赞";
                }
            }
            else
            {
                // 有权益卡但都无效，查询本地会员
                memberData = await _memberAppService.getMemberInfo(request);
                WriteLog("会员查询", "本地数据库会员卡结果", request.ToJson(), memberData.ToJson());

                // 检查本地会员的有效期，如果已过期则将STATUS改为3，并设置来源
                foreach (var member in memberData)
                {
                    if (member.EXPDATE.HasValue && member.EXPDATE.Value <= DateTime.Now)
                    {
                        member.STATUS = 3; // 设置为过期状态
                    }
                    member.SOURCE = "本地"; // 设置本地数据来源
                }

                // 如果本地没有会员或者权益卡过期，且有赞有基本信息，则构造注册用户信息
                bool shouldCreateRegisteredUser = false;
                if (memberData.Count == 0)
                {
                    // 本地没有会员
                    shouldCreateRegisteredUser = true;
                }
                else
                {
                    // 本地有会员，检查是否都已过期
                    var validLocalMembers = memberData.Where(m => m.EXPDATE.HasValue && m.EXPDATE.Value > DateTime.Now).ToList();
                    if (validLocalMembers.Count == 0)
                    {
                        // 本地会员都已过期
                        shouldCreateRegisteredUser = true;
                    }
                }

                if (shouldCreateRegisteredUser && yzMemberResult.BasicInfo != null)
                {
                    WriteLog("会员查询", "构造注册用户信息", request.ToJson(), "");

                    // 直接使用已查询的优惠券数量，避免重复调用
                    var couponNum = yzMemberResult.BasicInfo.COUPON_NUM;

                    // 构造注册用户信息
                    var registeredMember = new memberDto();
                    registeredMember.CODE = yzMemberResult.BasicInfo.PHONE; // 使用手机号作为会员卡号
                    registeredMember.MEMBER_NAME = "【注册用户】" + yzMemberResult.BasicInfo.MEMBER_NAME; // 注册用户名称特殊标识
                    registeredMember.CPTS = yzMemberResult.BasicInfo.CPTS; // 会员积分
                    registeredMember.PHONE = yzMemberResult.BasicInfo.PHONE; // 手机号
                    registeredMember.BIRTHDAY = yzMemberResult.BasicInfo.BIRTHDAY; // 生日
                    registeredMember.GRADE = "99"; // 注册用户等级设为99
                    registeredMember.DISC = 0m; // 注册用户无折扣优惠
                    registeredMember.STATUS = 1; // 状态设为使用中
                    registeredMember.EXPDATE = Conv.ToDate("2099-9-9"); // 设置长期有效期
                    registeredMember.CREDATE = yzMemberResult.BasicInfo.CREDATE; // 创建时间
                    registeredMember.COUPON_NUM = couponNum; // 优惠券数量
                    registeredMember.OPEN_ID = yzMemberResult.BasicInfo.OPEN_ID; // 有赞OpenID
                    registeredMember.SOURCE = "注册用户"; // 设置注册用户来源

                    memberData.Add(registeredMember);
                }
            }
        }
        else
        {
            // 如果有赞没有权益卡或未认证手机号，则查询本地会员
            memberData = await _memberAppService.getMemberInfo(request);
            WriteLog("会员查询", "本地数据库会员卡结果", request.ToJson(), memberData.ToJson());


            // 检查本地会员的有效期，如果已过期则将STATUS改为3，并设置来源
            foreach (var member in memberData)
            {
                if (member.EXPDATE.HasValue && member.EXPDATE.Value <= DateTime.Now)
                {
                    member.STATUS = 3; // 设置为过期状态
                }
                member.SOURCE = "本地"; // 设置本地数据来源
            }

            // 如果本地没有会员或者权益卡过期，且有赞有基本信息，则构造注册用户信息
            bool shouldCreateRegisteredUser = false;
            if (memberData.Count == 0)
            {
                // 本地没有会员
                shouldCreateRegisteredUser = true;
            }
            else
            {
                // 本地有会员，检查是否都已过期
                var validLocalMembers = memberData.Where(m => m.EXPDATE.HasValue && m.EXPDATE.Value > DateTime.Now).ToList();
                if (validLocalMembers.Count == 0)
                {
                    // 本地会员都已过期
                    shouldCreateRegisteredUser = true;
                }
            }

            if (shouldCreateRegisteredUser && yzMemberResult.BasicInfo != null)
            {

                // 直接使用已查询的优惠券数量，避免重复调用
                var couponNum = yzMemberResult.BasicInfo.COUPON_NUM;

                // 构造注册用户信息
                var registeredMember = new memberDto();
                registeredMember.CODE = yzMemberResult.BasicInfo.PHONE; // 使用手机号作为会员卡号
                registeredMember.MEMBER_NAME = "【注册用户】" + yzMemberResult.BasicInfo.MEMBER_NAME; // 注册用户名称特殊标识
                registeredMember.CPTS = yzMemberResult.BasicInfo.CPTS; // 会员积分
                registeredMember.PHONE = yzMemberResult.BasicInfo.PHONE; // 手机号
                registeredMember.BIRTHDAY = yzMemberResult.BasicInfo.BIRTHDAY; // 生日
                registeredMember.GRADE = "99"; // 注册用户等级设为99
                registeredMember.DISC = 0m; // 注册用户无折扣优惠
                registeredMember.STATUS = 1; // 状态设为使用中
                registeredMember.EXPDATE = Conv.ToDate("2099-9-9"); // 设置长期有效期
                registeredMember.CREDATE = yzMemberResult.BasicInfo.CREDATE; // 创建时间
                registeredMember.COUPON_NUM = couponNum; // 优惠券数量
                registeredMember.OPEN_ID = yzMemberResult.BasicInfo.OPEN_ID; // 有赞OpenID
                registeredMember.SOURCE = "注册用户"; // 设置注册用户来源

                memberData.Add(registeredMember);

                WriteLog("会员查询", "构造注册用户信息", request.ToJson(), registeredMember.ToJson());

            }
        }

        var jsonData = new
        {
            data = memberData, //数据
            total_page = 1,//总页数
            page = 1,//当前页
            records = memberData.Count,//总记录数
        };
        return Success(jsonData);
    }

    /// <summary>
    /// 有赞会员二维码查询
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    [HttpPost, Route("get_member_info_by_code")]
    public async Task<IActionResult> getMemberBuCodeInfo([FromBody] memberCodeRequest request)
    {
        var parameter = new YouzanParameter();
        parameter.url = $"/api/youzan.scrm.member.code.decode/1.0.0";
        var dict = new Dictionary<string, object>();
        dict.Add("keyword", request.code);
        parameter.body = dict;
        var response = await _youzanService.getYouzanData(parameter);


        WriteLog("会员二维码查询", parameter.url, parameter.body.ToJson(), response.ToJson());
        if (response.success)
        {
            var result = response.data.ToString().ToObject<customerDecodeResponse>();
            var jsonData = new { phone = result.mobile, open_id = result.yz_open_id };
            //var jsonData = new { phone = "54489649", open_id = "JwuxuK8E1017781081585225728" };
            return Success(jsonData);
        }
        return Fail(response.message);
    }

    /// <summary>
    /// 检查会员是否在黑名单中
    /// </summary>
    /// <param name="request">请求参数</param>
    /// <returns></returns>
    [HttpPost, Route("check_member_blacklist")]
    public async Task<IActionResult> CheckMemberBlacklist([FromBody] memberCodeRequest request)
    {
        if (string.IsNullOrEmpty(request.code))
            return Fail("会员号码不能为空");

        try
        {
            // 查询黑名单
            var blacklistInfo = await _memberAppService.GetMemberBlacklist(request.code);

            // 构造返回数据
            DataTable dt = new DataTable();
            dt.Columns.Add("reason", typeof(string));
            dt.Columns.Add("status", typeof(int));

            var row = dt.NewRow();
            row["reason"] = blacklistInfo?.reason ?? "";
            row["status"] = blacklistInfo?.status ?? 0;
            dt.Rows.Add(row);

            var jsonData = new
            {
                data = dt,
                total_page = 1,
                page = 1,
                records = dt.Rows.Count,
            };

            WriteLog("会员黑名单查询", "会员黑名单查询", request.ToJson(), jsonData.ToJson());
            return Success(jsonData);
        }
        catch (Exception ex)
        {
            return Fail(ex.Message);
        }
    }

    /// <summary>
    /// 获取有赞客户详情信息
    /// </summary>
    /// <param name="phone">手机号或有赞OpenID</param>
    /// <returns>
    /// 返回元组：
    /// - isAuthMobile: 是否已认证手机号
    /// - ValidCards: 所有权益卡列表（包含各种状态的权益卡）
    /// - BasicInfo: 会员基本信息（用于构造注册用户）
    /// </returns>
    /// <remarks>
    /// 该方法主要功能：
    /// 1. 根据手机号或OpenID查询有赞客户基础信息
    /// 2. 验证用户手机号认证状态
    /// 3. 查询用户优惠券数量
    /// 4. 如果用户有权益卡，返回所有权益卡并构造会员信息
    /// 5. 同时保存会员基本信息用于后续构造注册用户
    /// 6. 返回所有权益卡列表和基本信息
    /// </remarks>
    private async Task<(bool isAuthMobile, List<memberDto> ValidCards, memberDto BasicInfo)> getYzMemberInfo(string phone)
    {
        // 初始化返回数据
        var validCardsList = new List<memberDto>();
        memberDto basicInfo = null;

        // 构建有赞API请求参数
        var parameter = new YouzanParameter();
        parameter.url = $"/api/youzan.scrm.customer.detail.get/1.0.1";
        var request = new customerDetailRequest();
        request.fields = "user_base,benefit_cards,credit,auth_info"; // 指定需要返回的字段

        // 根据输入参数类型设置请求参数
        // 如果长度不是11位，则认为是有赞OpenID；否则认为是手机号
        if (phone.Trim().Length != 11)
        {
            request.account_info.account_type = 5; // OpenID类型
            request.yz_open_id = phone;
        }
        else
        {
            request.account_info.account_type = 2; // 手机号类型
            request.account_info.account_id = phone;
        }

        // 发送请求到有赞API
        parameter.body = request;
        var response = await _youzanService.getYouzanData(parameter);
        WriteLog("客户基础查询" + phone, parameter.url, request.ToJson(), response.ToJson());

        // 处理API响应成功的情况
        if (response.success)
        {
            // 解析有赞API返回的客户详情数据
            var customer_info = response.data.ToString().ToObject<customerDetailResponse>();

            // 构建基础会员信息对象（用于后续构造注册用户或权益卡信息）
            var memberInfo = new memberDto();
            // 会员名称逻辑：如果latest_nickname为空则显示show_name，否则显示"latest_nickname(show_name)"格式
            memberInfo.MEMBER_NAME = string.IsNullOrEmpty(customer_info.latest_nickname) ? customer_info.show_name : customer_info.latest_nickname + "(" + customer_info.show_name + ")";
            memberInfo.CPTS = customer_info.points; // 会员积分
            memberInfo.PHONE = customer_info.mobile; // 手机号
            memberInfo.BIRTHDAY = customer_info.birthday?.ToDate(); // 生日
            memberInfo.CREDATE = DataTimeHelper.LongTimeStampToDateTime(customer_info.created_at); // 创建时间

            // 保存基本信息用于后续构造注册用户
            basicInfo = memberInfo;

            // 验证手机号认证状态 - 未认证的用户不允许继续操作
            if (customer_info.auth_info?.is_mobile_auth == false)
            {
                WriteLog("检查是否已认证手机号" + phone, "未认证手机号，is_mobile_auth：" + customer_info.auth_info?.is_mobile_auth, "", "");
                return (false, validCardsList, basicInfo); // 返回认证失败状态
            }

            // 提前查询客户优惠券数量，避免在后续分支中重复调用
            var result_coupon_num = await getYzMemberCouponList(customer_info.yz_open_id);

            // 将优惠券数量设置到基本信息中，供后续使用
            basicInfo.COUPON_NUM = result_coupon_num;

            // 创建基础成员信息填充委托方法，用于统一处理会员信息的赋值逻辑
            // 参数说明：member-目标对象, code-会员卡号, grade-等级, disc-折扣, status-状态, expDate-有效期
            Action<memberDto, string, string, decimal, int, DateTime?> fillBaseMemberInfo = (member, code, grade, disc, status, expDate) =>
            {
                member.CODE = code; // 会员卡号
                member.MEMBER_NAME = memberInfo.MEMBER_NAME; // 会员名称
                member.CPTS = memberInfo.CPTS; // 会员积分
                member.PHONE = memberInfo.PHONE; // 手机号
                member.BIRTHDAY = memberInfo.BIRTHDAY; // 生日
                member.GRADE = grade; // 会员等级
                member.DISC = disc; // 折扣率
                member.STATUS = status; // 卡状态
                member.EXPDATE = expDate; // 有效期
                member.CREDATE = memberInfo.CREDATE; // 创建时间
                member.COUPON_NUM = result_coupon_num; // 优惠券数量
                member.OPEN_ID = customer_info.yz_open_id; // 有赞OpenID
                member.SOURCE = "有赞"; // 设置有赞数据来源
            };

            // 根据用户是否拥有权益卡进行不同的处理逻辑
            if (customer_info.cards.Count > 0)
            {
                // === 处理有权益卡的用户 ===

                // 1. 获取用户的有赞权益卡列表
                var result_equity = await getYzCustomerEquityList(customer_info.yz_open_id);
                var result_equity_response = (customerEquityResponse)result_equity.Data;
                var equity_list = result_equity_response.items;

                // 2. 获取权益模板配置表（优先从缓存获取，提高性能）
                var equity_template_list = await _cache.GetAsync<List<memberLzGradeDto>>(CacheKey.cacheGradeTemplate);
                if (equity_template_list == null)
                {
                    // 缓存未命中，从数据库加载权益模板表
                    equity_template_list = await _memberAppService.getMemberYzGradeList();
                    // 设置24小时缓存过期时间
                    await _cache.AddAsync(CacheKey.cacheGradeTemplate, equity_template_list, TimeSpan.FromHours(24));
                    WriteLog("权益模板表", "从数据库加载权益模板表并缓存", "", equity_template_list.ToJson());
                }

                // 3. 遍历用户的每张权益卡，返回所有权益卡
                foreach (var item in equity_list)
                {
                    // 根据权益卡别名查找对应的模板配置
                    var equity_template_info = equity_template_list.Where(x => x.lzCard == item.card_alias).FirstOrDefault();
                    if (equity_template_info == null)
                    {
                        // 记录未找到模板配置的异常情况
                        WriteLog("系统异常", "CRM_GRADE2LZ 表未找到有赞权益卡ID" + item.card_alias, "", "");
                    }

                    // 创建会员信息对象并填充数据
                    var member = new memberDto();
                    fillBaseMemberInfo(member,
                        item.card_no, // 权益卡号
                        equity_template_info?.Grade.ToString() ?? "0", // 会员等级（默认为0）
                        equity_template_info?.DISCOUNT ?? 0m, // 折扣率（默认无折扣）
                        item.card_state, // 权益卡状态(0-未激活;1-使用中;2-已退款;3-已过期;4-用户已删除;5-商家已禁用;6-管理员删除;7-系统删除;8-未生效)
                        Conv.ToDate(item.card_end_time)); // 权益卡有效期

                    validCardsList.Add(member);
                }

                WriteLog("会员卡信息" + phone, $"用户有权益卡，返回{validCardsList.Count}张权益卡", "", validCardsList.ToJson());
            }
            else
            {
                // === 处理无权益卡的用户 ===
                WriteLog("会员卡信息" + phone, "用户无权益卡", "", "");
            }

            // 返回成功状态、有效权益卡列表和基本信息
            return (true, validCardsList, basicInfo);
        }

        // API调用失败时返回失败状态和空数据
        return (false, validCardsList, basicInfo);
    }

    /// <summary>
    /// 获取有赞权益卡
    /// </summary>
    /// <param name="phone"></param>
    /// <returns></returns>
    private async Task<ApiResult> getYzCustomerEquityList(string phone)
    {
        var res = new ApiResult((int)BPM.AspNetCore.Mvc.StatusCode.Ok);
        var parameter = new YouzanParameter();
        parameter.url = $"/api/youzan.scrm.customer.card.list/4.0.0";
        var request = new customerEquityRequest();
        if (phone.Trim().Length != 11)
        {
            request.user.account_type = 5;
        }
        request.user.account_id = phone;
        var dict = new Dictionary<string, object>();
        dict.Add("params", request);
        parameter.body = dict;
        var response = await _youzanService.getYouzanData(parameter);
        WriteLog("客户权益查询", parameter.url, parameter.body.ToJson(), response.ToJson());
        if (!response.success)
        {
            res.info = response.message;
            return res;
        }
        res.Data = response.data.ToString().ToObject<customerEquityResponse>();
        return res;
    }

    /// <summary>
    /// 获取有赞优惠券列表
    /// </summary>
    /// <param name="phone"></param>
    /// <returns></returns>
    private async Task<int> getYzMemberCouponList(string yz_open_id)
    {
        var parameter = new YouzanParameter();
        parameter.url = $"/api/youzan.ump.voucher.query/3.0.0";
        var request = new memberCouponListRequest();
        request.yz_open_id = yz_open_id;
        parameter.body = request;
        var response = await _youzanService.getYouzanData(parameter);
        WriteLog("客户优惠券查询", parameter.url, parameter.body.ToJson(), response.ToJson());
        if (response.success)
            return response.total;
        return 0;
    }

    /// <summary>
    /// 通过18位code获取有赞客户信息
    /// </summary>
    /// <param name="code">18位code</param>
    /// <returns>包含手机号的客户信息</returns>
    private async Task<(bool success, string mobile, string yz_open_id, string card_alias)> getYzCustomerInfoBy18DigitCode(string code)
    {
        var parameter = new YouzanParameter();
        parameter.url = $"/api/youzan.scrm.customer.info.get/3.0.0";

        var dict = new Dictionary<string, object>();
        dict.Add("card_no", code); // 使用card_no参数
        parameter.body = dict;

        var response = await _youzanService.getYouzanData(parameter);
        WriteLog("通过用户权益卡号获取会员信息", parameter.url, parameter.body.ToJson(), response.ToJson());

        if (response.success && response.data != null)
        {
            // 解析响应数据
            try
            {
                // 获取mobile、yz_open_id、card_alias字段
                dynamic data = response.data;
                string mobile = data.mobile?.ToString();
                string yz_open_id = data.yz_open_id?.ToString();
                string card_alias = data.card_alias?.ToString();

                return (true, mobile, yz_open_id, card_alias);
            }
            catch (Exception ex)
            {
                WriteLog("通过用户权益卡号获取会员信息解析异常", parameter.url, ex.Message, response.ToJson());
                return (false, null, null, null);
            }
        }

        return (false, null, null, null);
    }

    /// <summary>
    /// 日志记录
    /// </summary>
    /// <param name="title">标题</param>
    /// <param name="pay_way">支付网关</param>
    /// <param name="requestData">请求数据</param>
    /// <param name="rawData">原始响应</param>
    private void WriteLog(string title, string pay_way, string requestData, string resultData)
    {
        var content = new StringBuilder();
        content.AppendLine($"请求地址:{pay_way}");
        content.AppendLine($"请求参数:{requestData}");
        content.AppendLine($"返回结果:{resultData}");
        Log.Set<LogContent>(p => p.Class = GetType().FullName)
            .Set<LogContent>(p => p.Caption = title)
            .Set<LogContent>(p => p.Content = content)
           .Info();
    }
}